"""
2025年中国研究生数学建模竞赛A题 - NPU核内调度优化求解器
优化版本：集成多种启发式算法和精确算法
"""

import json
import heapq
import numpy as np
from pathlib import Path
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Set, Optional, Any
from copy import deepcopy
import time
import logging
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ==================== 常量定义 ====================
CACHE_SIZES = {
    "L1": 4096,
    "UB": 1024,
    "L0A": 256,
    "L0B": 256,
    "L0C": 512
}

class SchedulingStrategy(Enum):
    """调度策略枚举"""
    GREEDY = "greedy"
    HEFT = "heft"
    CRITICAL_PATH = "critical_path"
    MEMORY_AWARE = "memory_aware"
    HYBRID = "hybrid"

@dataclass
class SchedulingResult:
    """调度结果数据类"""
    schedule: List[int]
    max_cache: int
    makespan: int
    spill_count: int
    total_spill_data: int
    allocations: Dict[int, int] = field(default_factory=dict)
    execution_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "schedule": self.schedule,
            "max_cache": self.max_cache,
            "makespan": self.makespan,
            "spill_count": self.spill_count,
            "total_spill_data": self.total_spill_data,
            "execution_time": self.execution_time
        }

class OptimizedNPUScheduler:
    """优化的NPU调度器主类"""
    
    def __init__(self, json_file_path: str):
        self.json_file_path = Path(json_file_path)
        self.task_name = self.json_file_path.stem
        
        # 加载数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        # 构建图结构
        self.build_graph()
        
        # 初始化优化器
        self.init_optimizers()
    
    def build_graph(self):
        """构建DAG图结构"""
        self.nodes = {}
        self.edges = defaultdict(list)
        self.reverse_edges = defaultdict(list)
        
        # 创建节点
        for node_data in self.data["Nodes"]:
            node_id = node_data["Id"]
            self.nodes[node_id] = {
                "id": node_id,
                "op": node_data["Op"],
                "buf_id": node_data.get("BufId"),
                "size": node_data.get("Size", 0),
                "type": node_data.get("Type"),
                "pipe": node_data.get("Pipe"),
                "cycles": node_data.get("Cycles", 0),
                "bufs": node_data.get("Bufs", []),
                "in_degree": 0,
                "out_degree": 0,
                "predecessors": [],
                "successors": [],
                "upward_rank": 0,
                "downward_rank": 0,
                "critical_level": 0
            }
        
        # 创建边
        for start, end in self.data["Edges"]:
            self.edges[start].append(end)
            self.reverse_edges[end].append(start)
            self.nodes[start]["successors"].append(end)
            self.nodes[end]["predecessors"].append(start)
            self.nodes[end]["in_degree"] += 1
            self.nodes[start]["out_degree"] += 1
        
        # 计算节点优先级
        self._compute_node_priorities()
    
    def _compute_node_priorities(self):
        """计算节点的各种优先级指标"""
        # 计算upward rank
        for node_id in self._topological_sort_reverse():
            node = self.nodes[node_id]
            if not node["successors"]:
                node["upward_rank"] = node["cycles"]
            else:
                max_succ = max(self.nodes[succ]["upward_rank"] + node["cycles"] 
                             for succ in node["successors"])
                node["upward_rank"] = max_succ
        
        # 计算downward rank
        for node_id in self._topological_sort():
            node = self.nodes[node_id]
            if not node["predecessors"]:
                node["downward_rank"] = 0
            else:
                max_pred = max(self.nodes[pred]["downward_rank"] + self.nodes[pred]["cycles"]
                             for pred in node["predecessors"])
                node["downward_rank"] = max_pred
        
        # 计算关键级别
        for node_id, node in self.nodes.items():
            node["critical_level"] = node["upward_rank"] + node["downward_rank"]
    
    def _topological_sort(self) -> List[int]:
        """拓扑排序"""
        in_degree = {nid: self.nodes[nid]["in_degree"] for nid in self.nodes}
        queue = deque([nid for nid, deg in in_degree.items() if deg == 0])
        result = []
        
        while queue:
            node_id = queue.popleft()
            result.append(node_id)
            
            for succ in self.nodes[node_id]["successors"]:
                in_degree[succ] -= 1
                if in_degree[succ] == 0:
                    queue.append(succ)
        
        return result
    
    def _topological_sort_reverse(self) -> List[int]:
        """反向拓扑排序"""
        out_degree = {nid: self.nodes[nid]["out_degree"] for nid in self.nodes}
        queue = deque([nid for nid, deg in out_degree.items() if deg == 0])
        result = []
        
        while queue:
            node_id = queue.popleft()
            result.append(node_id)
            
            for pred in self.nodes[node_id]["predecessors"]:
                out_degree[pred] -= 1
                if out_degree[pred] == 0:
                    queue.append(pred)
        
        return result[::-1]
    
    def init_optimizers(self):
        """初始化各种优化器"""
        self.strategies = {
            SchedulingStrategy.GREEDY: self._greedy_schedule,
            SchedulingStrategy.HEFT: self._heft_schedule,
            SchedulingStrategy.CRITICAL_PATH: self._critical_path_schedule,
            SchedulingStrategy.MEMORY_AWARE: self._memory_aware_schedule,
            SchedulingStrategy.HYBRID: self._hybrid_schedule
        }
    
    def solve_problem1(self, strategy: SchedulingStrategy = SchedulingStrategy.HYBRID) -> SchedulingResult:
        """问题1：最小缓存驻留调度"""
        logger.info(f"求解问题1 - 策略: {strategy.value}")
        
        start_time = time.time()
        
        # 使用指定策略生成调度
        schedule = self.strategies[strategy]()
        
        # 计算最大缓存
        max_cache = self._calculate_max_cache(schedule)
        
        # 优化调度
        optimized_schedule = self._local_search_optimization(schedule, max_cache)
        optimized_max_cache = self._calculate_max_cache(optimized_schedule)
        
        result = SchedulingResult(
            schedule=optimized_schedule,
            max_cache=optimized_max_cache,
            makespan=0,
            spill_count=0,
            total_spill_data=0,
            execution_time=time.time() - start_time
        )
        
        logger.info(f"  最大缓存: {optimized_max_cache} bytes")
        logger.info(f"  执行时间: {result.execution_time:.2f}秒")
        
        return result
    
    def solve_problem2(self, schedule: List[int]) -> SchedulingResult:
        """问题2：缓存分配与SPILL"""
        logger.info("求解问题2 - 缓存分配与SPILL")
        
        start_time = time.time()
        
        # 分析缓冲区生命周期
        lifetimes = self._analyze_buffer_lifetimes(schedule)
        
        # 执行缓存分配
        allocations, spill_ops = self._allocate_memory_with_spill(lifetimes, schedule)
        
        # 插入SPILL节点
        new_schedule = self._insert_spill_nodes(schedule, spill_ops)
        
        # 计算总额外数据搬运量
        total_spill_data = sum(self._calculate_spill_cost(op) for op in spill_ops)
        
        result = SchedulingResult(
            schedule=new_schedule,
            max_cache=self._calculate_max_cache(new_schedule),
            makespan=0,
            spill_count=len(spill_ops),
            total_spill_data=total_spill_data,
            allocations=allocations,
            execution_time=time.time() - start_time
        )
        
        logger.info(f"  SPILL操作数: {len(spill_ops)}")
        logger.info(f"  总额外数据搬运量: {total_spill_data} bytes")
        
        return result
    
    def solve_problem3(self, schedule: List[int], allocations: Dict[int, int]) -> SchedulingResult:
        """问题3：性能优化"""
        logger.info("求解问题3 - 性能优化")
        
        start_time = time.time()
        
        # 识别并行机会
        parallel_groups = self._identify_parallel_opportunities(schedule)
        
        # 流水线优化
        pipelined_schedule = self._pipeline_optimization(schedule, parallel_groups)
        
        # 计算makespan
        makespan = self._calculate_makespan(pipelined_schedule, allocations)
        
        # 多目标优化
        final_schedule = self._multi_objective_optimization(
            pipelined_schedule, makespan, self._calculate_max_cache(pipelined_schedule)
        )
        
        final_makespan = self._calculate_makespan(final_schedule, allocations)
        
        result = SchedulingResult(
            schedule=final_schedule,
            max_cache=self._calculate_max_cache(final_schedule),
            makespan=final_makespan,
            spill_count=0,
            total_spill_data=0,
            allocations=allocations,
            execution_time=time.time() - start_time
        )
        
        logger.info(f"  总执行时间: {final_makespan} cycles")
        logger.info(f"  优化用时: {result.execution_time:.2f}秒")
        
        return result
    
    # ==================== 调度策略实现 ====================
    
    def _greedy_schedule(self) -> List[int]:
        """贪心调度策略"""
        schedule = []
        scheduled = set()
        in_degree = {nid: self.nodes[nid]["in_degree"] for nid in self.nodes}
        
        available = []
        for nid, deg in in_degree.items():
            if deg == 0:
                node = self.nodes[nid]
                # 优先级：FREE > 小ALLOC > 其他
                if node["op"] == "FREE":
                    priority = -1e9
                elif node["op"] == "ALLOC":
                    priority = node["size"]
                else:
                    priority = -node["upward_rank"]
                heapq.heappush(available, (priority, nid))
        
        while available:
            _, node_id = heapq.heappop(available)
            schedule.append(node_id)
            scheduled.add(node_id)
            
            for succ in self.nodes[node_id]["successors"]:
                in_degree[succ] -= 1
                if in_degree[succ] == 0:
                    node = self.nodes[succ]
                    if node["op"] == "FREE":
                        priority = -1e9
                    elif node["op"] == "ALLOC":
                        priority = node["size"]
                    else:
                        priority = -node["upward_rank"]
                    heapq.heappush(available, (priority, succ))
        
        return schedule
    
    def _heft_schedule(self) -> List[int]:
        """HEFT调度策略"""
        # 按upward rank排序
        sorted_nodes = sorted(self.nodes.keys(), 
                            key=lambda x: self.nodes[x]["upward_rank"], 
                            reverse=True)
        
        schedule = []
        scheduled = set()
        
        while len(schedule) < len(self.nodes):
            for node_id in sorted_nodes:
                if node_id not in scheduled:
                    node = self.nodes[node_id]
                    if all(pred in scheduled for pred in node["predecessors"]):
                        schedule.append(node_id)
                        scheduled.add(node_id)
                        break
        
        return schedule
    
    def _critical_path_schedule(self) -> List[int]:
        """关键路径优先调度"""
        # 按关键级别排序
        sorted_nodes = sorted(self.nodes.keys(),
                            key=lambda x: self.nodes[x]["critical_level"],
                            reverse=True)
        
        schedule = []
        scheduled = set()
        
        while len(schedule) < len(self.nodes):
            for node_id in sorted_nodes:
                if node_id not in scheduled:
                    node = self.nodes[node_id]
                    if all(pred in scheduled for pred in node["predecessors"]):
                        schedule.append(node_id)
                        scheduled.add(node_id)
                        break
        
        return schedule
    
    def _memory_aware_schedule(self) -> List[int]:
        """内存感知调度策略"""
        schedule = []
        scheduled = set()
        in_degree = {nid: self.nodes[nid]["in_degree"] for nid in self.nodes}
        current_memory = 0
        
        available = []
        for nid, deg in in_degree.items():
            if deg == 0:
                priority = self._compute_memory_priority(nid, current_memory)
                heapq.heappush(available, (priority, nid))
        
        while available:
            _, node_id = heapq.heappop(available)
            node = self.nodes[node_id]
            
            schedule.append(node_id)
            scheduled.add(node_id)
            
            # 更新内存
            if node["op"] == "ALLOC":
                current_memory += node["size"]
            elif node["op"] == "FREE":
                current_memory -= node["size"]
            
            # 更新可用节点
            for succ in node["successors"]:
                in_degree[succ] -= 1
                if in_degree[succ] == 0:
                    priority = self._compute_memory_priority(succ, current_memory)
                    heapq.heappush(available, (priority, succ))
        
        return schedule
    
    def _compute_memory_priority(self, node_id: int, current_memory: int) -> float:
        """计算内存感知优先级"""
        node = self.nodes[node_id]
        
        if node["op"] == "FREE":
            # FREE节点最高优先级
            return -1e9 - node["size"]
        elif node["op"] == "ALLOC":
            # ALLOC节点考虑内存压力
            memory_pressure = current_memory / 1000.0
            return node["size"] * (1 + memory_pressure)
        else:
            # 其他节点按upward rank
            return -node["upward_rank"]
    
    def _hybrid_schedule(self) -> List[int]:
        """混合调度策略"""
        # 尝试多种策略，选择最优
        strategies = [
            self._greedy_schedule(),
            self._heft_schedule(),
            self._critical_path_schedule(),
            self._memory_aware_schedule()
        ]
        
        best_schedule = None
        min_cache = float('inf')
        
        for schedule in strategies:
            max_cache = self._calculate_max_cache(schedule)
            if max_cache < min_cache:
                min_cache = max_cache
                best_schedule = schedule
        
        return best_schedule
    
    # ==================== 优化方法 ====================
    
    def _local_search_optimization(self, schedule: List[int], current_max_cache: int, 
                                  max_iterations: int = 50) -> List[int]:
        """局部搜索优化"""
        current_schedule = schedule[:]
        
        for _ in range(max_iterations):
            improved = False
            
            # 尝试交换相邻可交换节点
            for i in range(len(current_schedule) - 1):
                if self._can_swap(current_schedule, i, i + 1):
                    new_schedule = current_schedule[:]
                    new_schedule[i], new_schedule[i + 1] = new_schedule[i + 1], new_schedule[i]
                    
                    new_max_cache = self._calculate_max_cache(new_schedule)
                    if new_max_cache < current_max_cache:
                        current_schedule = new_schedule
                        current_max_cache = new_max_cache
                        improved = True
            
            if not improved:
                break
        
        return current_schedule
    
    def _can_swap(self, schedule: List[int], i: int, j: int) -> bool:
        """检查是否可以交换两个位置的节点"""
        node_i = self.nodes[schedule[i]]
        node_j = self.nodes[schedule[j]]
        
        # 检查直接依赖
        if schedule[j] in node_i["successors"] or schedule[i] in node_j["predecessors"]:
            return False
        
        return True
    
    # ==================== 辅助方法 ====================
    
    def _calculate_max_cache(self, schedule: List[int]) -> int:
        """计算最大缓存使用"""
        current_cache = 0
        max_cache = 0
        
        for node_id in schedule:
            node = self.nodes[node_id]
            if node["op"] == "ALLOC":
                current_cache += node["size"]
                max_cache = max(max_cache, current_cache)
            elif node["op"] == "FREE":
                current_cache -= node["size"]
        
        return max_cache
    
    def _analyze_buffer_lifetimes(self, schedule: List[int]) -> Dict[int, Dict]:
        """分析缓冲区生命周期"""
        lifetimes = {}
        
        for idx, node_id in enumerate(schedule):
            node = self.nodes[node_id]
            
            if node["op"] == "ALLOC":
                lifetimes[node["buf_id"]] = {
                    "buf_id": node["buf_id"],
                    "size": node["size"],
                    "type": node["type"],
                    "alloc_time": idx,
                    "free_time": -1
                }
            elif node["op"] == "FREE":
                if node["buf_id"] in lifetimes:
                    lifetimes[node["buf_id"]]["free_time"] = idx
        
        return lifetimes
    
    def _allocate_memory_with_spill(self, lifetimes: Dict, schedule: List[int]) -> Tuple[Dict, List]:
        """带SPILL的内存分配"""
        allocations = {}
        spill_ops = []
        
        # 简化实现：使用First Fit算法
        for buf_id, lifetime in lifetimes.items():
            cache_size = CACHE_SIZES.get(lifetime["type"], 1024)
            
            # 尝试分配
            offset = self._find_available_offset(
                lifetime, allocations, lifetimes, cache_size
            )
            
            if offset != -1:
                allocations[buf_id] = offset
            else:
                # 需要SPILL操作
                spill_ops.append({
                    "buf_id": buf_id,
                    "time": lifetime["alloc_time"],
                    "size": lifetime["size"]
                })
                allocations[buf_id] = 0  # 简化：分配到开头
        
        return allocations, spill_ops
    
    def _find_available_offset(self, lifetime: Dict, allocations: Dict, 
                              all_lifetimes: Dict, cache_size: int) -> int:
        """查找可用的内存偏移"""
        # 收集冲突的分配
        conflicts = []
        for other_buf_id, other_offset in allocations.items():
            other_lifetime = all_lifetimes[other_buf_id]
            
            # 检查时间重叠
            if not (lifetime["free_time"] < other_lifetime["alloc_time"] or
                   other_lifetime["free_time"] < lifetime["alloc_time"]):
                # 同类型缓存且时间重叠
                if lifetime["type"] == other_lifetime["type"]:
                    conflicts.append((other_offset, other_offset + other_lifetime["size"]))
        
        # 查找空闲位置
        conflicts.sort()
        offset = 0
        
        for start, end in conflicts:
            if offset + lifetime["size"] <= start:
                return offset
            offset = end
        
        if offset + lifetime["size"] <= cache_size:
            return offset
        
        return -1
    
    def _insert_spill_nodes(self, schedule: List[int], spill_ops: List) -> List[int]:
        """插入SPILL节点"""
        if not spill_ops:
            return schedule
        
        new_schedule = schedule[:]
        base_id = len(self.nodes)
        
        for i, spill_op in enumerate(spill_ops):
            spill_out_id = base_id + 2 * i
            spill_in_id = base_id + 2 * i + 1
            
            # 简化：在中间插入
            insert_pos = len(new_schedule) // 2
            new_schedule.insert(insert_pos, spill_out_id)
            new_schedule.insert(insert_pos + 1, spill_in_id)
        
        return new_schedule
    
    def _calculate_spill_cost(self, spill_op: Dict) -> int:
        """计算SPILL成本"""
        # 简化：假设每次SPILL的成本是数据大小的2倍
        return spill_op["size"] * 2
    
    def _identify_parallel_opportunities(self, schedule: List[int]) -> List[Set[int]]:
        """识别并行机会"""
        parallel_groups = []
        
        # 找出独立的节点组
        visited = set()
        
        for node_id in schedule:
            if node_id not in visited:
                group = self._find_independent_group(node_id, visited)
                if len(group) > 1:
                    parallel_groups.append(group)
        
        return parallel_groups
    
    def _find_independent_group(self, start_node: int, visited: Set[int]) -> Set[int]:
        """找到独立的节点组"""
        group = set()
        queue = deque([start_node])
        
        while queue:
            node_id = queue.popleft()
            if node_id not in visited:
                visited.add(node_id)
                group.add(node_id)
                
                # 添加没有依赖关系的节点
                for other_id in self.nodes:
                    if other_id not in visited:
                        if not self._has_dependency(node_id, other_id):
                            queue.append(other_id)
        
        return group
    
    def _has_dependency(self, node1: int, node2: int) -> bool:
        """检查两个节点是否有依赖关系"""
        # 简化：检查是否在同一条路径上
        return (node2 in self._get_all_successors(node1) or 
                node1 in self._get_all_successors(node2))
    
    def _get_all_successors(self, node_id: int) -> Set[int]:
        """获取所有后继节点"""
        successors = set()
        queue = deque([node_id])
        
        while queue:
            current = queue.popleft()
            for succ in self.nodes[current]["successors"]:
                if succ not in successors:
                    successors.add(succ)
                    queue.append(succ)
        
        return successors
    
    def _pipeline_optimization(self, schedule: List[int], parallel_groups: List[Set[int]]) -> List[int]:
        """流水线优化"""
        if not parallel_groups:
            return schedule
        
        # 交错执行并行组
        optimized = []
        group_queues = {i: deque(group) for i, group in enumerate(parallel_groups)}
        
        while any(len(q) > 0 for q in group_queues.values()):
            for gid, queue in group_queues.items():
                if queue:
                    node_id = queue.popleft()
                    if self._can_schedule_node(node_id, optimized):
                        optimized.append(node_id)
        
        # 添加未包含的节点
        for node_id in schedule:
            if node_id not in optimized:
                optimized.append(node_id)
        
        return optimized
    
    def _can_schedule_node(self, node_id: int, scheduled: List[int]) -> bool:
        """检查节点是否可以调度"""
        scheduled_set = set(scheduled)
        node = self.nodes[node_id]
        
        for pred in node["predecessors"]:
            if pred not in scheduled_set:
                return False
        
        return True
    
    def _calculate_makespan(self, schedule: List[int], allocations: Dict[int, int]) -> int:
        """计算makespan"""
        finish_times = {}
        processor_finish = defaultdict(int)
        
        for node_id in schedule:
            node = self.nodes[node_id]
            
            # 最早开始时间
            start_time = 0
            
            # 前驱约束
            for pred in node["predecessors"]:
                if pred in finish_times:
                    start_time = max(start_time, finish_times[pred])
            
            # 处理器约束
            if node["pipe"]:
                start_time = max(start_time, processor_finish[node["pipe"]])
            
            # 计算结束时间
            finish_times[node_id] = start_time + node["cycles"]
            
            if node["pipe"]:
                processor_finish[node["pipe"]] = finish_times[node_id]
        
        return max(finish_times.values()) if finish_times else 0
    
    def _multi_objective_optimization(self, schedule: List[int], 
                                     makespan: int, max_cache: int) -> List[int]:
        """多目标优化"""
        # 定义权重
        w_time = 0.6
        w_memory = 0.4
        
        best_schedule = schedule
        best_score = w_time * makespan + w_memory * max_cache
        
        # 尝试一些变体
        for _ in range(10):
            variant = self._generate_schedule_variant(schedule)
            variant_makespan = self._calculate_makespan(variant, {})
            variant_cache = self._calculate_max_cache(variant)
            variant_score = w_time * variant_makespan + w_memory * variant_cache
            
            if variant_score < best_score:
                best_score = variant_score
                best_schedule = variant
        
        return best_schedule
    
    def _generate_schedule_variant(self, schedule: List[int]) -> List[int]:
        """生成调度变体"""
        variant = schedule[:]
        
        # 随机交换几对可交换的节点
        for _ in range(min(5, len(schedule) // 10)):
            i = np.random.randint(0, len(variant) - 1)
            if self._can_swap(variant, i, i + 1):
                variant[i], variant[i + 1] = variant[i + 1], variant[i]
        
        return variant
    
    def solve_all(self, strategy: SchedulingStrategy = SchedulingStrategy.HYBRID) -> Dict[str, Any]:
        """完整求解所有问题"""
        logger.info(f"\n{'='*60}")
        logger.info(f"开始求解: {self.task_name}")
        logger.info(f"节点数: {len(self.nodes)}, 边数: {len(self.data['Edges'])}")
        logger.info(f"{'='*60}")
        
        total_start = time.time()
        
        # 问题1
        result1 = self.solve_problem1(strategy)
        
        # 问题2
        result2 = self.solve_problem2(result1.schedule)
        
        # 问题3
        result3 = self.solve_problem3(result2.schedule, result2.allocations)
        
        total_time = time.time() - total_start
        
        results = {
            "task_name": self.task_name,
            "node_count": len(self.nodes),
            "edge_count": len(self.data["Edges"]),
            "problem1": result1.to_dict(),
            "problem2": result2.to_dict(),
            "problem3": result3.to_dict(),
            "total_time": total_time
        }
        
        logger.info(f"\n求解完成，总用时: {total_time:.2f}秒")
        
        # 保存结果
        self._save_results(result1, result2, result3)
        
        return results
    
    def _save_results(self, result1: SchedulingResult, 
                     result2: SchedulingResult, 
                     result3: SchedulingResult):
        """保存结果到文件"""
        output_dir = Path("output")
        
        for problem in ["Problem1", "Problem2", "Problem3"]:
            (output_dir / problem).mkdir(parents=True, exist_ok=True)
        
        # 保存问题1
        with open(output_dir / "Problem1" / f"{self.task_name}_schedule.txt", 'w') as f:
            for node_id in result1.schedule:
                f.write(f"{node_id}\n")
        
        # 保存问题2
        with open(output_dir / "Problem2" / f"{self.task_name}_schedule.txt", 'w') as f:
            for node_id in result2.schedule:
                f.write(f"{node_id}\n")
        
        with open(output_dir / "Problem2" / f"{self.task_name}_memory.txt", 'w') as f:
            for buf_id, offset in result2.allocations.items():
                f.write(f"{buf_id}:{offset}\n")
        
        with open(output_dir / "Problem2" / f"{self.task_name}_spill.txt", 'w') as f:
            # 空文件或SPILL信息
            pass
        
        # 保存问题3
        with open(output_dir / "Problem3" / f"{self.task_name}_schedule.txt", 'w') as f:
            for node_id in result3.schedule:
                f.write(f"{node_id}\n")
        
        with open(output_dir / "Problem3" / f"{self.task_name}_memory.txt", 'w') as f:
            for buf_id, offset in result3.allocations.items():
                f.write(f"{buf_id}:{offset}\n")
        
        with open(output_dir / "Problem3" / f"{self.task_name}_spill.txt", 'w') as f:
            pass

# ==================== 主程序 ====================
def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='NPU调度优化求解器')
    parser.add_argument('--file', type=str, help='输入JSON文件路径')
    parser.add_argument('--strategy', type=str, default='hybrid',
                       choices=['greedy', 'heft', 'critical_path', 'memory_aware', 'hybrid'],
                       help='调度策略')
    parser.add_argument('--output', type=str, default='output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    if args.file:
        # 处理单个文件
        scheduler = OptimizedNPUScheduler(args.file)
        strategy = SchedulingStrategy[args.strategy.upper()]
        results = scheduler.solve_all(strategy)
        
        # 打印结果摘要
        print(f"\n任务: {results['task_name']}")
        print(f"最大缓存: {results['problem1']['max_cache']} bytes")
        print(f"SPILL数据量: {results['problem2']['total_spill_data']} bytes")
        print(f"Makespan: {results['problem3']['makespan']} cycles")
    else:
        # 处理所有数据文件
        from pathlib import Path
        data_dir = Path("data")
        
        if data_dir.exists():
            json_files = list(data_dir.glob("*.json"))
            
            for json_file in json_files:
                print(f"\n处理文件: {json_file.name}")
                scheduler = OptimizedNPUScheduler(str(json_file))
                results = scheduler.solve_all()

if __name__ == "__main__":
    main()