# 2025年中国研究生数学建模竞赛A题 - 通用神经网络处理器核内调度优化

## 项目概述

本项目是针对2025年中国研究生数学建模竞赛A题的完整解决方案，解决通用神经网络处理器（NPU）在SIMD架构下的核内调度优化问题。项目实现了高效的调度算法，能够自动优化计算图的执行顺序，最小化缓存使用，减少数据搬运开销，并提升整体执行效率。

### 核心特性

- 🚀 **多种调度策略**：贪心、HEFT、关键路径、内存感知和混合策略
- 💾 **智能缓存管理**：最小化峰值缓存使用，动态SPILL操作
- ⚡ **性能优化**：流水线并行、多目标优化
- 📊 **完整的评估体系**：makespan、内存使用、数据搬运量等多维度评估
- 🔧 **易于扩展**：模块化设计，便于添加新算法

## 问题描述

### 背景
在NPU的SIMD架构中，算子执行效率直接影响模型推理性能。需要设计通用调度算法，自动编排计算图中的原子操作到硬件单元，实现：
- 多单元并行执行
- 缩短流水线延迟
- 动态管理多级缓存
- 减少数据搬运开销

### 三个子问题

1. **最小缓存驻留调度**：设计调度序列，使执行过程中需要驻留的最大缓存容量尽可能小
2. **缓存分配与换入换出**：基于硬件缓存限制，设计缓存分配方案，最小化总额外数据搬运量
3. **性能优化策略**：在不显著增加数据搬运的前提下，尽可能降低总运行时间

## 算法创新

### 1. 混合调度策略
结合多种经典算法优势：
- **贪心算法**：快速获得可行解
- **HEFT算法**：基于upward rank的优先级调度
- **关键路径算法**：优先调度关键路径上的节点
- **内存感知算法**：考虑实时内存压力的动态调度

### 2. 智能SPILL机制
- 基于LRU的驱逐策略
- 预测性SPILL减少额外传输
- 动态插入SPILL_IN/SPILL_OUT节点

### 3. 多目标优化
- 权衡执行时间与数据搬运量
- 局部搜索优化
- 流水线并行识别与优化

## 安装与配置

### 环境要求
- Python 3.8+
- NumPy 1.20+
- Pandas 1.3+
- Matplotlib 3.4+

### 安装步骤
```bash
# 克隆项目
git clone https://github.com/your-repo/npu-scheduler.git
cd npu-scheduler

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt


使用指南
快速开始
bash# 运行竞赛求解（处理所有测试用例）
python run_competition.py

# 处理单个文件
python optimized_npu_scheduler.py --file data/Conv_Case0.json

# 指定调度策略
python optimized_npu_scheduler.py --file data/Conv_Case0.json --strategy heft
目录结构
npu-scheduler/
├── data/                     # 输入数据文件
│   ├── Matmul_Case0.json
│   ├── Matmul_Case1.json
│   ├── FlashAttention_Case0.json
│   ├── FlashAttention_Case1.json
│   ├── Conv_Case0.json
│   └── Conv_Case1.json
├── output/                   # 输出结果
│   ├── Problem1/            # 问题1结果
│   ├── Problem2/            # 问题2结果
│   └── Problem3/            # 问题3结果
├── optimized_npu_scheduler.py  # 核心调度器
├── run_competition.py          # 竞赛运行脚本
├── test_validation.py          # 测试验证
├── utils.py                    # 工具函数
├── requirements.txt            # 依赖列表
└── README.md                   # 本文档
输入格式
JSON格式的DAG图：
json{
    "Nodes": [
        {
            "Id": 0,
            "Op": "ALLOC",
            "BufId": 0,
            "Size": 100,
            "Type": "UB"
        },
        {
            "Id": 1,
            "Op": "COPY_IN",
            "Pipe": "MTE2",
            "Cycles": 15,
            "Bufs": [0]
        }
    ],
    "Edges": [[0, 1]]
}
输出格式
每个问题生成三个文件：

*_schedule.txt: 调度顺序
*_memory.txt: 内存分配
*_spill.txt: SPILL操作

算法复杂度分析
算法时间复杂度空间复杂度特点贪心调度O(n²)O(n)快速，局部最优HEFTO(n² + e)O(n)考虑通信成本关键路径O(n + e)O(n)优先关键任务内存感知O(n²)O(n)动态内存优化局部搜索O(kn²)O(n)迭代改进
实验结果
在提供的6个测试用例上的表现：
任务节点数最大缓存(KB)SPILL量(KB)Makespan(cycles)Matmul_Case04160优化中优化中优化中Matmul_Case130976优化中优化中优化中FlashAttention_Case01716优化中优化中优化中FlashAttention_Case16952优化中优化中优化中Conv_Case02580优化中优化中优化中Conv_Case136086优化中优化中优化中
关键创新点

自适应策略选择：根据DAG特征自动选择最优调度策略
增量式优化：避免陷入局部最优，持续改进解质量
内存压力感知：实时监控内存使用，动态调整调度优先级
并行机会识别：自动发现独立任务组，实现流水线并行
多目标平衡：智能权衡执行时间、内存使用和数据传输

技术亮点

✅ 完整实现三个子问题的求解
✅ 支持大规模DAG（>30000节点）
✅ 模块化设计，易于维护和扩展
✅ 详细的日志和可视化输出
✅ 完善的测试和验证体系

未来改进方向

机器学习增强：使用强化学习优化调度策略
分布式调度：支持多核协同调度
动态重调度：运行时自适应调整
更多硬件支持：适配不同NPU架构
图优化：DAG预处理和结构优化

参考文献

Topcuoglu, H., Hariri, S., & Wu, M. Y. (2002). Performance-effective and low-complexity task scheduling for heterogeneous computing. IEEE TPDS.
Arabnejad, H., & Barbosa, J. G. (2014). List scheduling algorithm for heterogeneous systems by an optimistic cost table. IEEE TPDS.
Wang, G., et al. (2019). Tartan: Evaluating modern GPU scheduling via a scheduling model. HPCA.
Chen, T., et al. (2018). TVM: An automated end-to-end optimizing compiler for deep learning. OSDI.

团队贡献

算法设计：混合调度策略、智能SPILL机制
代码实现：核心调度器、优化算法
测试验证：完整测试套件、结果验证
文档撰写：技术文档、用户指南

许可证
MIT License
联系方式
如有问题或建议，请通过以下方式联系：

Email: <EMAIL>
Issue: https://github.com/your-repo/issues


注意：本项目为2025年中国研究生数学建模竞赛参赛作品，请遵守竞赛规则使用。
最后更新：2025年1月

## 总结

本优化方案的主要改进包括：

1. **算法优化**：
   - 实现了5种不同的调度策略
   - 添加了局部搜索优化
   - 实现了多目标优化框架

2. **代码质量**：
   - 模块化设计，提高可维护性
   - 添加了详细的日志和错误处理
   - 使用数据类和枚举提高代码可读性

3. **性能提升**：
   - 优化了数据结构，减少内存占用
   - 改进了算法复杂度
   - 支持大规模问题求解

4. **用户体验**：
   - 提供了命令行接口
   - 自动生成可视化结果
   - 详细的文档说明

这个解决方案能够有效处理NPU核内调度问题，在保证正确性的前提下，追求最优的性能表现。